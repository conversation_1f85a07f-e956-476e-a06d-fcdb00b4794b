# ROS Bag Torque Data Visualization

This directory contains Python scripts to read and visualize torque data from ROS bag files.

## Files

1. **`visualize_torque_data.py`** - Full-featured script with command-line arguments and error handling
2. **`simple_torque_visualizer.py`** - Simplified version for quick visualization
3. **`README_torque_visualization.md`** - This documentation file

## Requirements

Make sure you have the following Python packages installed:

```bash
pip install rosbag matplotlib numpy
```

Or if using ROS:
```bash
sudo apt-get install python3-rosbag python3-matplotlib python3-numpy
```

## Usage

### Method 1: Full-featured script

```bash
# Basic usage (uses default bag file name)
python3 visualize_torque_data.py

# Specify bag file
python3 visualize_torque_data.py your_bag_file.bag

# Specify topic and output file
python3 visualize_torque_data.py your_bag_file.bag --topic /joint_torques --output my_plot.png

# Get help
python3 visualize_torque_data.py --help
```

### Method 2: Simple script

```bash
python3 simple_torque_visualizer.py
```

To use a different bag file, edit the `bag_filename` variable in the script.

## Expected Data Format

The scripts expect ROS bag files containing `std_msgs/Float64MultiArray` messages with:

- **Message type**: `std_msgs/Float64MultiArray`
- **Data format**: Array of 12 float values representing joint torques
- **Example data**: `[0.335, -4.820, 1.703, 0.565, 0.929, 0.224, -0.258, -4.793, 1.667, -0.478, 0.935, 0.234]`

## Output

Both scripts will:

1. **Print information** about available topics in the bag file
2. **Extract torque data** from the appropriate topic
3. **Create a time-series plot** showing all 12 joint torques
4. **Save the plot** as a PNG image file
5. **Display the plot** on screen

## Joint Naming Convention

The scripts assume a standard quadruped robot with 12 joints:

- Joints 1-3: Front Left (FL_hip, FL_thigh, FL_calf)
- Joints 4-6: Front Right (FR_hip, FR_thigh, FR_calf)
- Joints 7-9: Rear Left (RL_hip, RL_thigh, RL_calf)
- Joints 10-12: Rear Right (RR_hip, RR_thigh, RR_calf)

## Troubleshooting

### Common Issues:

1. **"Bag file not found"**
   - Make sure the bag file is in the same directory as the script
   - Or provide the full path to the bag file

2. **"No suitable topics found"**
   - Check that your bag file contains `std_msgs/Float64MultiArray` messages
   - Use `rosbag info your_bag_file.bag` to see available topics

3. **"Import error for rosbag"**
   - Make sure ROS is properly installed and sourced
   - Or install rosbag separately: `pip install rosbag`

4. **"Message has unexpected format"**
   - The script expects exactly 12 torque values per message
   - Check your data format and modify the script if needed

### Customization:

You can modify the scripts to:
- Change joint names in the `joint_names` list
- Adjust plot colors and styling
- Add additional data processing or filtering
- Change the output file format (PNG, PDF, SVG, etc.)

## Example Output

The visualization will show:
- Time-series plot with 12 colored lines (one per joint)
- X-axis: Time in seconds
- Y-axis: Torque in Newton-meters (Nm)
- Legend with joint names/numbers
- Grid for easier reading
- Statistics box with data summary

## Contact

If you encounter any issues or need modifications, please let me know!
