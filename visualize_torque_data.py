#!/usr/bin/env python3
"""
ROS Bag Torque Data Visualization Script

This script reads torque data from a ROS bag file and creates a time-series plot
showing torque curves for all 12 robot joints.

Author: Assistant
Date: 2025-07-23
"""

import rosbag
import matplotlib.pyplot as plt
import numpy as np
import os
import sys
from std_msgs.msg import Float64MultiArray
import argparse
from datetime import datetime


def read_torque_data_from_bag(bag_file_path, topic_name=None):
    """
    Read torque data from ROS bag file.
    
    Args:
        bag_file_path (str): Path to the ROS bag file
        topic_name (str): Specific topic name to read from (optional)
        
    Returns:
        tuple: (timestamps, torque_data) where timestamps is a list of time values
               and torque_data is a list of 12-element torque arrays
    """
    timestamps = []
    torque_data = []
    
    try:
        with rosbag.Bag(bag_file_path, 'r') as bag:
            # Get bag info
            info = bag.get_type_and_topic_info()
            topics = info.topics
            
            print(f"Available topics in bag file:")
            for topic, topic_info in topics.items():
                print(f"  - {topic}: {topic_info.msg_type} ({topic_info.message_count} messages)")
            
            # Find torque-related topics if no specific topic is provided
            if topic_name is None:
                torque_topics = []
                for topic in topics.keys():
                    if 'torque' in topic.lower() or 'effort' in topic.lower():
                        if topics[topic].msg_type == 'std_msgs/Float64MultiArray':
                            torque_topics.append(topic)
                
                if not torque_topics:
                    print("No torque topics found. Looking for any Float64MultiArray topics...")
                    for topic in topics.keys():
                        if topics[topic].msg_type == 'std_msgs/Float64MultiArray':
                            torque_topics.append(topic)
                
                if not torque_topics:
                    raise ValueError("No suitable topics found in the bag file")
                
                topic_name = torque_topics[0]
                print(f"Using topic: {topic_name}")
            
            # Read messages from the selected topic
            start_time = None
            for topic, msg, t in bag.read_messages(topics=[topic_name]):
                if start_time is None:
                    start_time = t.to_sec()
                
                # Convert ROS time to relative time in seconds
                timestamp = t.to_sec() - start_time
                timestamps.append(timestamp)
                
                # Extract torque data
                if hasattr(msg, 'data') and len(msg.data) >= 12:
                    torque_data.append(msg.data[:12])  # Take first 12 values
                else:
                    print(f"Warning: Message at time {timestamp:.3f}s has unexpected format")
                    continue
            
            print(f"Successfully read {len(timestamps)} messages from topic '{topic_name}'")
            
    except Exception as e:
        print(f"Error reading bag file: {e}")
        raise
    
    return timestamps, torque_data


def plot_torque_data(timestamps, torque_data, output_file="torque_plot.png"):
    """
    Create and save a time-series plot of torque data.
    
    Args:
        timestamps (list): List of timestamp values
        torque_data (list): List of 12-element torque arrays
        output_file (str): Output file name for the plot
    """
    if not timestamps or not torque_data:
        raise ValueError("No data to plot")
    
    # Convert to numpy arrays for easier manipulation
    timestamps = np.array(timestamps)
    torque_data = np.array(torque_data)
    
    # Create the plot
    plt.figure(figsize=(15, 10))
    
    # Define colors for each joint
    colors = plt.cm.tab20(np.linspace(0, 1, 12))
    
    # Joint names (assuming standard quadruped robot joint naming)
    joint_names = [
        'FL_hip', 'FL_thigh', 'FL_calf',      # Front Left
        'FR_hip', 'FR_thigh', 'FR_calf',      # Front Right
        'RL_hip', 'RL_thigh', 'RL_calf',      # Rear Left
        'RR_hip', 'RR_thigh', 'RR_calf'       # Rear Right
    ]
    
    # Plot each joint's torque data
    for i in range(12):
        plt.plot(timestamps, torque_data[:, i], 
                color=colors[i], 
                label=f'Joint {i+1} ({joint_names[i]})',
                linewidth=1.5,
                alpha=0.8)
    
    # Customize the plot
    plt.xlabel('Time (seconds)', fontsize=12)
    plt.ylabel('Torque (Nm)', fontsize=12)
    plt.title('Robot Joint Torques Over Time', fontsize=14, fontweight='bold')
    plt.grid(True, alpha=0.3)
    plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left', fontsize=10)
    
    # Add statistics text box
    stats_text = f"Data points: {len(timestamps)}\n"
    stats_text += f"Duration: {timestamps[-1]:.2f}s\n"
    stats_text += f"Max torque: {np.max(torque_data):.2f} Nm\n"
    stats_text += f"Min torque: {np.min(torque_data):.2f} Nm"
    
    plt.text(0.02, 0.98, stats_text, transform=plt.gca().transAxes, 
             verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
    
    # Adjust layout to prevent legend cutoff
    plt.tight_layout()
    
    # Save the plot
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    print(f"Plot saved as: {output_file}")
    
    # Show the plot
    plt.show()


def main():
    """Main function to run the torque visualization script."""
    parser = argparse.ArgumentParser(description='Visualize torque data from ROS bag file')
    parser.add_argument('bag_file', nargs='?', default='torque_2025-07-23-14-49-46.bag',
                       help='Path to the ROS bag file')
    parser.add_argument('--topic', '-t', type=str, default=None,
                       help='Specific topic name to read torque data from')
    parser.add_argument('--output', '-o', type=str, default='torque_plot.png',
                       help='Output file name for the plot')
    
    args = parser.parse_args()
    
    # Check if bag file exists
    if not os.path.exists(args.bag_file):
        print(f"Error: Bag file '{args.bag_file}' not found!")
        print("Please make sure the bag file is in the current directory or provide the correct path.")
        sys.exit(1)
    
    try:
        print(f"Reading torque data from: {args.bag_file}")
        timestamps, torque_data = read_torque_data_from_bag(args.bag_file, args.topic)
        
        if not timestamps:
            print("No torque data found in the bag file!")
            sys.exit(1)
        
        print(f"Creating visualization...")
        plot_torque_data(timestamps, torque_data, args.output)
        
        print("Visualization completed successfully!")
        
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
