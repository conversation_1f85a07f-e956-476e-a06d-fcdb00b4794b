# 
policy_path: "/home/<USER>/YuSongmin/RL_Leggedgym/unitree_rl_gym-main/logs/go2_handstand_first/exported/policies/policy_1.pt"
xml_path: "{LEGGED_GYM_ROOT_DIR}/resources/robots/go2/go2/scene.xml"

# Total simulation time
simulation_duration: 60.0
# Simulation time step
simulation_dt: 0.002
# Controller update frequency (meets the requirement of simulation_dt * controll_decimation=0.02; 50Hz)
control_decimation: 10

kps: [40,40,40,40,40,40,40,40,40,40,40,40]
# kps: [20,20,20,20,20,20,20,20,20,20,20,20]
kds: [1,1,1,1,1,1,1,1,1,1,1,1]
# kds: [0.5,0.5,0.5,0.5,0.5,0.5,0.5,0.5,0.5,0.5,0.5,0.5]

default_angles: [0.1,0.8,-1.5,
                -0.1,0.8,-1.5,
                 0.1,1.0,-1.5,
                -0.1,1. ,-1.5]
# default_angles: [0.,1.25,-2.,
#                 -0.,1.25,-2.,
#                  0.,1.25,-2.,
#                 -0.,1.25 ,-2.]
lin_vel_scale : 2.0
ang_vel_scale: 0.25
dof_pos_scale: 1.0
dof_vel_scale: 0.05
action_scale: 0.25
cmd_scale: [2.0, 2.0, 0.25]
num_actions: 12
num_obs: 48

cmd_init: [0, 0, 0]