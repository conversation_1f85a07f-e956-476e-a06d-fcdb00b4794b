#!/usr/bin/env python3
"""
Simple ROS Bag Torque Data Visualization Script

A simplified version for quick torque data visualization from ROS bag files.
"""

import rosbag
import matplotlib.pyplot as plt
import numpy as np
import os


def visualize_torque_bag(bag_file="torque_2025-07-23-14-49-46.bag"):
    """
    Simple function to visualize torque data from ROS bag.
    
    Args:
        bag_file (str): Path to the ROS bag file
    """
    
    # Check if file exists
    if not os.path.exists(bag_file):
        print(f"Error: File '{bag_file}' not found!")
        return
    
    timestamps = []
    torque_data = []
    
    try:
        # Open and read the bag file
        print(f"Opening bag file: {bag_file}")
        bag = rosbag.Bag(bag_file, 'r')
        
        # Get all topics
        topics = bag.get_type_and_topic_info().topics
        print("Available topics:")
        for topic_name, topic_info in topics.items():
            print(f"  {topic_name}: {topic_info.msg_type}")
        
        # Find the first Float64MultiArray topic (assuming it's torque data)
        torque_topic = None
        for topic_name, topic_info in topics.items():
            if topic_info.msg_type == 'std_msgs/Float64MultiArray':
                torque_topic = topic_name
                break
        
        if torque_topic is None:
            print("No Float64MultiArray topic found!")
            bag.close()
            return
        
        print(f"Using topic: {torque_topic}")
        
        # Read messages
        start_time = None
        for topic, msg, t in bag.read_messages(topics=[torque_topic]):
            if start_time is None:
                start_time = t.to_sec()
            
            # Convert to relative time
            rel_time = t.to_sec() - start_time
            timestamps.append(rel_time)
            
            # Extract torque data (first 12 values)
            if len(msg.data) >= 12:
                torque_data.append(msg.data[:12])
            else:
                print(f"Warning: Message has only {len(msg.data)} values, expected 12")
                torque_data.append(msg.data + [0.0] * (12 - len(msg.data)))
        
        bag.close()
        
        if not timestamps:
            print("No data found!")
            return
        
        print(f"Read {len(timestamps)} data points")
        
        # Convert to numpy arrays
        timestamps = np.array(timestamps)
        torque_data = np.array(torque_data)
        
        # Create the plot
        plt.figure(figsize=(12, 8))
        
        # Plot all 12 joints
        for i in range(12):
            plt.plot(timestamps, torque_data[:, i], label=f'Joint {i+1}', linewidth=1.2)
        
        plt.xlabel('Time (seconds)')
        plt.ylabel('Torque (Nm)')
        plt.title('Robot Joint Torques')
        plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        
        # Save and show
        output_file = "torque_visualization.png"
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        print(f"Plot saved as: {output_file}")
        plt.show()
        
    except Exception as e:
        print(f"Error: {e}")


if __name__ == "__main__":
    # You can change the bag file name here
    bag_filename = "torque_2025-07-23-14-49-46.bag"
    visualize_torque_bag(bag_filename)
