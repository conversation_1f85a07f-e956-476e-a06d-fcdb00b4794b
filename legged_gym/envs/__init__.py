from legged_gym import LEGGED_GYM_ROOT_DIR, LEGGED_GYM_ENVS_DIR

from legged_gym.envs.MoB.JUMP.jump_env import GO2_JUMP_Robot
from legged_gym.envs.MoB.JUMP.GO2_JUMP_config import GO2_JUMP_Cfg_Yu,GO2_JUMP_PPO_Yu


from legged_gym.envs.MoB.Trot.Trot import GO2_Trot_Robot
from legged_gym.envs.MoB.Trot.GO2_Trot_config import GO2_Trot_Cfg_Yu,GO2_Trot_PPO_Yu

from legged_gym.envs.Flip.BackFlip.BackFlip_env import Go2_BackFlip
from legged_gym.envs.Flip.BackFlip.GO2_BackFlip_Config import GO2_BackFlip_Cfg_Yu, GO2_BackFlip_PPO_Yu


from .base.legged_robot import LeggedRobot
from .Stand.Handstand.handstand import Go2_stand
from .Stand.Leftstand.handstand import Go2_stand_Robot

from legged_gym.envs.Stand.Handstand.Go2_handstand_Config import GO2Cfg_Handstand,GO2CfgPPO_Handstand
from .Stand.Leftstand.Go2_handstand_Config import GO2Cfg_Handstand_Command,GO2CfgPPO_Handstand_Command
from legged_gym.envs.Stand.Handstand.robs3go_handstand_Config import Robs3goCfg_Handstand,Robs3goCfgPPO_Handstand
from legged_gym.envs.Stand.Handstand.ysc4go_handstand_Config import Ysc4goCfg_Handstand,Ysc4goCfgPPO_Handstand

from legged_gym.utils.task_registry import task_registry


from legged_gym.envs.Flip.Spring_Jump.Spring_JUMP_env import Go2_Spring_Jump
from legged_gym.envs.Flip.Spring_Jump.GO2_Spring_JUMP_config import GO2_Spring_JUMP_Cfg_Yu,GO2_Spring_JUMP_PPO_Yu




task_registry.register( "go2_trot", GO2_Trot_Robot, GO2_Trot_Cfg_Yu(), GO2_Trot_PPO_Yu())
task_registry.register( "go2_jump", GO2_JUMP_Robot, GO2_JUMP_Cfg_Yu(), GO2_JUMP_PPO_Yu())
task_registry.register( "go2_handstand", Go2_stand, GO2Cfg_Handstand(), GO2CfgPPO_Handstand())
task_registry.register( "go2_handstand_command", Go2_stand_Robot, GO2Cfg_Handstand_Command(), GO2CfgPPO_Handstand_Command())
task_registry.register( "go2_spring_jump", Go2_Spring_Jump, GO2_Spring_JUMP_Cfg_Yu(), GO2_Spring_JUMP_PPO_Yu())
task_registry.register( "go2_backflip", Go2_BackFlip, GO2_BackFlip_Cfg_Yu(), GO2_BackFlip_PPO_Yu())

task_registry.register( "robs3go_handstand", Go2_stand, Robs3goCfg_Handstand(), Robs3goCfgPPO_Handstand())
task_registry.register( "ysc4go_handstand", Go2_stand, Ysc4goCfg_Handstand(), Ysc4goCfgPPO_Handstand())

print("注册的任务:  ",task_registry.task_classes)
